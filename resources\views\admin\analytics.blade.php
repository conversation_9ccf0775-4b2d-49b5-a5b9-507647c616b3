@extends('layouts.admin')

@section('title', 'Analytics Dashboard')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="admin-card p-8 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-pink-600 bg-clip-text text-transparent">
                    Analytics Dashboard
                </h1>
                <p class="text-gray-600 mt-2 text-lg">System insights and performance metrics</p>
                <div class="flex items-center mt-3 text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Real-time data and trend analysis
                </div>
            </div>
            <div class="text-right">
                <div class="bg-gradient-to-r from-orange-500 to-pink-600 text-white px-6 py-3 rounded-xl shadow-lg">
                    <p class="text-sm font-medium opacity-90">Data as of</p>
                    <p class="text-lg font-bold">{{ now()->format('M d, Y H:i') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="admin-card p-6 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">New Users (30d)</p>
                    <p class="text-3xl font-bold text-gray-900">{{ number_format($user_trends->sum('count')) }}</p>
                    <p class="text-xs text-green-600 font-medium">+{{ number_format(($user_trends->sum('count') / max($user_trends->count(), 1)) * 100, 1) }}% avg/day</p>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">New Posts (30d)</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $post_trends->sum('count') }}</p>
                    <p class="text-xs text-green-600">+{{ number_format(($post_trends->sum('count') / max($post_trends->count(), 1)) * 100, 1) }}% avg/day</p>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Active Users</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $active_users->count() }}</p>
                    <p class="text-xs text-blue-600">Top contributors</p>
                </div>
            </div>
        </div>

        <div class="bg-white shadow rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Engagement</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ $popular_posts->sum('reactions_count') + $popular_posts->sum('comments_count') }}</p>
                    <p class="text-xs text-orange-600">Reactions + Comments</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- User Registration Trends -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">User Registration Trends (Last 30 Days)</h3>
            <div class="h-64 flex items-end justify-between space-x-1">
                @php
                    $maxCount = $user_trends->max('count') ?: 1;
                @endphp
                @forelse($user_trends as $trend)
                    <div class="flex flex-col items-center">
                        <div class="bg-blue-500 rounded-t" style="height: {{ ($trend->count / $maxCount) * 200 }}px; width: 20px;"></div>
                        <span class="text-xs text-gray-500 mt-1 transform -rotate-45 origin-top-left">
                            {{ \Carbon\Carbon::parse($trend->date)->format('m/d') }}
                        </span>
                    </div>
                @empty
                    <p class="text-gray-500 text-center w-full">No data available</p>
                @endforelse
            </div>
        </div>

        <!-- Post Creation Trends -->
        <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Post Creation Trends (Last 30 Days)</h3>
            <div class="h-64 flex items-end justify-between space-x-1">
                @php
                    $maxPostCount = $post_trends->max('count') ?: 1;
                @endphp
                @forelse($post_trends as $trend)
                    <div class="flex flex-col items-center">
                        <div class="bg-green-500 rounded-t" style="height: {{ ($trend->count / $maxPostCount) * 200 }}px; width: 20px;"></div>
                        <span class="text-xs text-gray-500 mt-1 transform -rotate-45 origin-top-left">
                            {{ \Carbon\Carbon::parse($trend->date)->format('m/d') }}
                        </span>
                    </div>
                @empty
                    <p class="text-gray-500 text-center w-full">No data available</p>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Top Contributors and Popular Posts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Most Active Users -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Most Active Users</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @forelse($active_users as $user)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                        <span class="text-sm font-medium text-gray-700">{{ substr($user->name, 0, 1) }}</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                    <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm font-medium text-gray-900">{{ $user->posts_count }} posts</div>
                                <div class="text-sm text-gray-500">{{ $user->comments_count }} comments</div>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-center py-4">No active users found</p>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Popular Posts -->
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Most Popular Posts</h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    @forelse($popular_posts as $post)
                        <div class="border-l-4 border-blue-500 pl-4">
                            <div class="text-sm font-medium text-gray-900">
                                {{ Str::limit($post->title ?: 'Untitled Post', 50) }}
                            </div>
                            <div class="text-sm text-gray-500">
                                {{ Str::limit(strip_tags($post->content), 80) }}
                            </div>
                            <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500">
                                <span>{{ $post->reactions_count }} reactions</span>
                                <span>{{ $post->comments_count }} comments</span>
                                <span>{{ $post->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-center py-4">No popular posts found</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">System Health Overview</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <p class="text-sm font-medium text-gray-900">Database Performance</p>
                <p class="text-xs text-green-600">Excellent</p>
                <p class="text-xs text-gray-500 mt-1">Avg query time: &lt;50ms</p>
            </div>
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                </div>
                <p class="text-sm font-medium text-gray-900">Server Response</p>
                <p class="text-xs text-blue-600">Good</p>
                <p class="text-xs text-gray-500 mt-1">Avg response: &lt;200ms</p>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                    </svg>
                </div>
                <p class="text-sm font-medium text-gray-900">Storage Usage</p>
                <p class="text-xs text-purple-600">Normal</p>
                <p class="text-xs text-gray-500 mt-1">65% of allocated space</p>
            </div>
        </div>
    </div>
</div>
@endsection
