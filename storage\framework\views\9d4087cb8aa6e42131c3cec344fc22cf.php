<?php $__env->startSection('title', 'Organizations Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Organizations Management</h1>
                <p class="text-gray-600">Monitor and manage all organizations in the system</p>
            </div>
        </div>
    </div>

    <!-- Organization Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600"><?php echo e($org_stats['total']); ?></p>
                <p class="text-sm text-gray-600">Total Organizations</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600"><?php echo e($org_stats['active']); ?></p>
                <p class="text-sm text-gray-600">Active</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-600"><?php echo e($org_stats['inactive']); ?></p>
                <p class="text-sm text-gray-600">Inactive</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600"><?php echo e($org_stats['pending']); ?></p>
                <p class="text-sm text-gray-600">Pending</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="GET" action="<?php echo e(route('admin.organizations.index')); ?>" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Organization name or description..." 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Status</option>
                        <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                        <option value="inactive" <?php echo e(request('status') === 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                    </select>
                </div>

                <!-- Type Filter -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
                    <select name="type" id="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Types</option>
                        <option value="academic" <?php echo e(request('type') === 'academic' ? 'selected' : ''); ?>>Academic</option>
                        <option value="student_org" <?php echo e(request('type') === 'student_org' ? 'selected' : ''); ?>>Student Organization</option>
                        <option value="club" <?php echo e(request('type') === 'club' ? 'selected' : ''); ?>>Club</option>
                        <option value="department" <?php echo e(request('type') === 'department' ? 'selected' : ''); ?>>Department</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Organizations Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Organizations (<?php echo e($organizations->total()); ?>)</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Creator</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Posts</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $organizations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $organization): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <?php if($organization->logo): ?>
                                            <img class="h-12 w-12 rounded-lg object-cover" src="<?php echo e(Storage::url($organization->logo)); ?>" alt="<?php echo e($organization->name); ?>">
                                        <?php else: ?>
                                            <div class="h-12 w-12 rounded-lg bg-gray-300 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700"><?php echo e(substr($organization->name, 0, 2)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($organization->name); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e(Str::limit($organization->description, 60)); ?></div>
                                        <?php if($organization->type): ?>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $organization->type))); ?>

                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-700"><?php echo e(substr($organization->creator->name, 0, 1)); ?></span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($organization->creator->name); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($organization->creator->email); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    <?php if($organization->status === 'active'): ?> bg-green-100 text-green-800
                                    <?php elseif($organization->status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                    <?php else: ?> bg-red-100 text-red-800 <?php endif; ?>">
                                    <?php echo e(ucfirst($organization->status)); ?>

                                </span>
                                <?php if($organization->is_page_mode): ?>
                                    <br>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800 mt-1">
                                        Page Mode
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($organization->members_count); ?> members
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($organization->posts_count); ?> posts
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($organization->created_at->format('M d, Y')); ?>

                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex flex-col space-y-1">
                                    <a href="<?php echo e(route('organizations.show', $organization)); ?>" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    <button class="text-blue-600 hover:text-blue-900 text-left">Members</button>
                                    <?php if($organization->status === 'pending'): ?>
                                        <button class="text-green-600 hover:text-green-900 text-left">Approve</button>
                                        <button class="text-red-600 hover:text-red-900 text-left">Reject</button>
                                    <?php endif; ?>
                                    <button class="text-yellow-600 hover:text-yellow-900 text-left">Edit</button>
                                    <?php if($organization->status !== 'active'): ?>
                                        <button class="text-red-600 hover:text-red-900 text-left">Delete</button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No organizations found matching your criteria.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($organizations->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($organizations->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/admin/organizations/index.blade.php ENDPATH**/ ?>