@extends('layouts.admin')

@section('title', 'Groups Management')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Groups Management</h1>
                <p class="text-gray-600">Monitor and manage all groups in the system</p>
            </div>
        </div>
    </div>

    <!-- Group Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ $group_stats['total'] }}</p>
                <p class="text-sm text-gray-600">Total Groups</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ $group_stats['public'] }}</p>
                <p class="text-sm text-gray-600">Public Groups</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-purple-600">{{ $group_stats['private'] }}</p>
                <p class="text-sm text-gray-600">Private Groups</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="GET" action="{{ route('admin.groups.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           placeholder="Group name or description..." 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>

                <!-- Privacy Filter -->
                <div>
                    <label for="privacy" class="block text-sm font-medium text-gray-700">Privacy</label>
                    <select name="privacy" id="privacy" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Privacy</option>
                        <option value="public" {{ request('privacy') === 'public' ? 'selected' : '' }}>Public</option>
                        <option value="private" {{ request('privacy') === 'private' ? 'selected' : '' }}>Private</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Groups Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Groups ({{ $groups->total() }})</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Creator</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Privacy</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Posts</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($groups as $group)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        @if($group->cover_image)
                                            <img class="h-12 w-12 rounded-lg object-cover" src="{{ Storage::url($group->cover_image) }}" alt="{{ $group->name }}">
                                        @else
                                            <div class="h-12 w-12 rounded-lg bg-gray-300 flex items-center justify-center">
                                                <span class="text-sm font-medium text-gray-700">{{ substr($group->name, 0, 2) }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ $group->name }}</div>
                                        <div class="text-sm text-gray-500">{{ Str::limit($group->description, 60) }}</div>
                                        @if($group->category)
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                                {{ ucfirst($group->category) }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-700">{{ substr($group->creator->name, 0, 1) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">{{ $group->creator->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $group->creator->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    @if($group->privacy === 'public') bg-green-100 text-green-800
                                    @else bg-purple-100 text-purple-800 @endif">
                                    {{ ucfirst($group->privacy) }}
                                </span>
                                @if($group->requires_approval)
                                    <br>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 mt-1">
                                        Approval Required
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $group->members_count }} members
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $group->posts_count }} posts
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ $group->created_at->format('M d, Y') }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex flex-col space-y-1">
                                    <a href="{{ route('groups.show', $group) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    <button class="text-blue-600 hover:text-blue-900 text-left">Members</button>
                                    <button class="text-green-600 hover:text-green-900 text-left">Posts</button>
                                    <button class="text-yellow-600 hover:text-yellow-900 text-left">Edit</button>
                                    <button class="text-red-600 hover:text-red-900 text-left">Delete</button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No groups found matching your criteria.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($groups->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $groups->appends(request()->query())->links() }}
            </div>
        @endif
    </div>
</div>
@endsection
