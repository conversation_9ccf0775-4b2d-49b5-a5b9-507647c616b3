@extends('layouts.admin')

@section('title', 'Scholarships Management')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Scholarships Management</h1>
                <p class="text-gray-600">Monitor and manage all scholarship listings in the system</p>
            </div>
            <div>
                <a href="{{ route('scholarships.create') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    Add New Scholarship
                </a>
            </div>
        </div>
    </div>

    <!-- Scholarship Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600">{{ $scholarship_stats['total'] }}</p>
                <p class="text-sm text-gray-600">Total Scholarships</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600">{{ $scholarship_stats['active'] }}</p>
                <p class="text-sm text-gray-600">Active</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-600">{{ $scholarship_stats['inactive'] }}</p>
                <p class="text-sm text-gray-600">Inactive</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600">{{ $scholarship_stats['expired'] }}</p>
                <p class="text-sm text-gray-600">Expired</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="GET" action="{{ route('admin.scholarships.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="{{ request('search') }}" 
                           placeholder="Title or provider..." 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Scholarships Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Scholarships ({{ $scholarships->total() }})</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scholarship</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deadline</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($scholarships as $scholarship)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="max-w-xs">
                                    <div class="text-sm font-medium text-gray-900">{{ $scholarship->title }}</div>
                                    <div class="text-sm text-gray-500">{{ Str::limit($scholarship->description, 100) }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $scholarship->provider }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    @if($scholarship->amount)
                                        ${{ number_format($scholarship->amount, 2) }}
                                    @else
                                        <span class="text-gray-500">Not specified</span>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    {{ $scholarship->deadline ? $scholarship->deadline->format('M d, Y') : 'No deadline' }}
                                </div>
                                @if($scholarship->deadline && $scholarship->deadline->isPast())
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                        Expired
                                    </span>
                                @elseif($scholarship->deadline && $scholarship->deadline->diffInDays() <= 7)
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                        Expiring Soon
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    @if($scholarship->status === 'active') bg-green-100 text-green-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ ucfirst($scholarship->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-700">{{ substr($scholarship->creator->name, 0, 1) }}</span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900">{{ $scholarship->creator->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $scholarship->created_at->format('M d, Y') }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex flex-col space-y-1">
                                    <a href="{{ route('scholarships.show', $scholarship) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    <a href="{{ route('scholarships.edit', $scholarship) }}" class="text-yellow-600 hover:text-yellow-900">Edit</a>
                                    @if($scholarship->status === 'active')
                                        <button class="text-red-600 hover:text-red-900 text-left">Deactivate</button>
                                    @else
                                        <button class="text-green-600 hover:text-green-900 text-left">Activate</button>
                                    @endif
                                    <button class="text-red-600 hover:text-red-900 text-left">Delete</button>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No scholarships found matching your criteria.
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($scholarships->hasPages())
            <div class="px-6 py-4 border-t border-gray-200">
                {{ $scholarships->appends(request()->query())->links() }}
            </div>
        @endif
    </div>

    <!-- Quick Stats -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Insights</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <p class="text-2xl font-bold text-blue-600">
                    ${{ number_format($scholarships->where('status', 'active')->sum('amount'), 2) }}
                </p>
                <p class="text-sm text-blue-800">Total Active Scholarship Value</p>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <p class="text-2xl font-bold text-green-600">
                    {{ $scholarships->where('deadline', '>=', now())->count() }}
                </p>
                <p class="text-sm text-green-800">Available Scholarships</p>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                <p class="text-2xl font-bold text-yellow-600">
                    {{ $scholarships->where('deadline', '<=', now()->addDays(30))->where('deadline', '>=', now())->count() }}
                </p>
                <p class="text-sm text-yellow-800">Expiring in 30 Days</p>
            </div>
        </div>
    </div>
</div>
@endsection
