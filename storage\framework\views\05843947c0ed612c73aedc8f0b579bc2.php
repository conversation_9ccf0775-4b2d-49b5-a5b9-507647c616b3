<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'Admin Dashboard'); ?> - <?php echo e(config('app.name', 'UniLink')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <!-- Additional Admin Styles -->
    <style>
        .admin-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .admin-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }
        .admin-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 16px;
            padding: 1.5rem;
            color: white;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: scale(1.05);
        }
        .nav-link-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .table-row:hover {
            background: linear-gradient(90deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .action-btn {
            transition: all 0.2s ease;
            border-radius: 6px;
            padding: 0.25rem 0.75rem;
        }
        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
    <div class="min-h-screen">
        <!-- Admin Navigation -->
        <nav class="admin-gradient shadow-lg border-b border-indigo-600">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex">
                        <!-- Logo -->
                        <div class="flex-shrink-0 flex items-center">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-xl font-bold text-white flex items-center space-x-2">
                                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                                <span>UniLink Admin</span>
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <div class="hidden space-x-1 sm:-my-px sm:ml-10 sm:flex">
                            <a href="<?php echo e(route('admin.dashboard')); ?>" class="<?php if(request()->routeIs('admin.dashboard')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                                </svg>
                                Dashboard
                            </a>
                            <a href="<?php echo e(route('admin.users.index')); ?>" class="<?php if(request()->routeIs('admin.users.*')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                </svg>
                                Users
                            </a>
                            <a href="<?php echo e(route('admin.posts.index')); ?>" class="<?php if(request()->routeIs('admin.posts.*')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                                </svg>
                                Posts
                            </a>
                            <a href="<?php echo e(route('admin.organizations.index')); ?>" class="<?php if(request()->routeIs('admin.organizations.*')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                </svg>
                                Organizations
                            </a>
                            <a href="<?php echo e(route('admin.groups.index')); ?>" class="<?php if(request()->routeIs('admin.groups.*')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                </svg>
                                Groups
                            </a>
                            <a href="<?php echo e(route('admin.scholarships.index')); ?>" class="<?php if(request()->routeIs('admin.scholarships.*')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                Scholarships
                            </a>
                            <a href="<?php echo e(route('admin.analytics')); ?>" class="<?php if(request()->routeIs('admin.analytics')): ?> bg-white bg-opacity-20 text-white <?php else: ?> text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 <?php endif; ?> inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                </svg>
                                Analytics
                            </a>
                        </div>
                    </div>

                    <!-- Settings Dropdown -->
                    <div class="hidden sm:flex sm:items-center sm:ml-6">
                        <div class="ml-3 relative">
                            <div class="flex items-center space-x-4">
                                <!-- Back to Main Site -->
                                <a href="<?php echo e(route('dashboard')); ?>" class="text-white text-opacity-80 hover:text-white hover:bg-white hover:bg-opacity-10 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                    </svg>
                                    Back to Site
                                </a>

                                <!-- User Dropdown -->
                                <div class="relative" x-data="{ open: false }">
                                    <button @click="open = !open" class="flex items-center text-sm rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 bg-white bg-opacity-10 px-3 py-2 hover:bg-opacity-20 transition-all duration-200">
                                        <span class="sr-only">Open user menu</span>
                                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                            <span class="text-sm font-medium text-white"><?php echo e(substr(auth()->user()->name, 0, 1)); ?></span>
                                        </div>
                                        <span class="ml-2 text-sm font-medium text-white"><?php echo e(auth()->user()->name); ?></span>
                                        <svg class="ml-1 w-4 h-4 text-white text-opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>

                                    <div x-show="open" @click.away="open = false" x-transition class="origin-top-right absolute right-0 mt-2 w-48 rounded-xl shadow-lg bg-white ring-1 ring-black ring-opacity-5 overflow-hidden">
                                        <div class="py-1">
                                            <a href="<?php echo e(route('profile.edit')); ?>" class="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                                                <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                </svg>
                                                Profile Settings
                                            </a>
                                            <div class="border-t border-gray-100"></div>
                                            <form method="POST" action="<?php echo e(route('logout')); ?>">
                                                <?php echo csrf_field(); ?>
                                                <button type="submit" class="flex items-center w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200">
                                                    <svg class="w-4 h-4 mr-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                                                    </svg>
                                                    Sign Out
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile menu button -->
                    <div class="-mr-2 flex items-center sm:hidden" x-data="{ mobileMenuOpen: false }">
                        <button type="button" @click="mobileMenuOpen = !mobileMenuOpen" class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500">
                            <span class="sr-only">Open main menu</span>
                            <svg class="block h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                            </svg>
                        </button>

                        <!-- Mobile menu -->
                        <div x-show="mobileMenuOpen" @click.away="mobileMenuOpen = false" x-transition class="absolute top-16 right-4 w-64 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50">
                            <div class="py-1">
                                <a href="<?php echo e(route('admin.dashboard')); ?>" class="<?php if(request()->routeIs('admin.dashboard')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Dashboard</a>
                                <a href="<?php echo e(route('admin.users.index')); ?>" class="<?php if(request()->routeIs('admin.users.*')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Users</a>
                                <a href="<?php echo e(route('admin.posts.index')); ?>" class="<?php if(request()->routeIs('admin.posts.*')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Posts</a>
                                <a href="<?php echo e(route('admin.organizations.index')); ?>" class="<?php if(request()->routeIs('admin.organizations.*')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Organizations</a>
                                <a href="<?php echo e(route('admin.groups.index')); ?>" class="<?php if(request()->routeIs('admin.groups.*')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Groups</a>
                                <a href="<?php echo e(route('admin.scholarships.index')); ?>" class="<?php if(request()->routeIs('admin.scholarships.*')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Scholarships</a>
                                <a href="<?php echo e(route('admin.analytics')); ?>" class="<?php if(request()->routeIs('admin.analytics')): ?> bg-indigo-50 text-indigo-700 <?php else: ?> text-gray-700 hover:bg-gray-100 <?php endif; ?> block px-4 py-2 text-sm">Analytics</a>
                                <div class="border-t border-gray-200 my-1"></div>
                                <a href="<?php echo e(route('dashboard')); ?>" class="text-gray-700 hover:bg-gray-100 block px-4 py-2 text-sm">Back to Site</a>
                                <a href="<?php echo e(route('profile.edit')); ?>" class="text-gray-700 hover:bg-gray-100 block px-4 py-2 text-sm">Profile</a>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="text-gray-700 hover:bg-gray-100 block w-full text-left px-4 py-2 text-sm">Sign out</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Breadcrumb Navigation -->
        <?php if(!request()->routeIs('admin.dashboard')): ?>
            <div class="bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="py-3">
                        <nav class="flex" aria-label="Breadcrumb">
                            <ol class="flex items-center space-x-4">
                                <li>
                                    <div>
                                        <a href="<?php echo e(route('admin.dashboard')); ?>" class="text-gray-400 hover:text-gray-500">
                                            <svg class="flex-shrink-0 h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                                            </svg>
                                            <span class="sr-only">Dashboard</span>
                                        </a>
                                    </div>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                        <span class="ml-4 text-sm font-medium text-gray-500">
                                            <?php if(request()->routeIs('admin.users.*')): ?>
                                                Users
                                            <?php elseif(request()->routeIs('admin.posts.*')): ?>
                                                Posts
                                            <?php elseif(request()->routeIs('admin.organizations.*')): ?>
                                                Organizations
                                            <?php elseif(request()->routeIs('admin.groups.*')): ?>
                                                Groups
                                            <?php elseif(request()->routeIs('admin.scholarships.*')): ?>
                                                Scholarships
                                            <?php elseif(request()->routeIs('admin.analytics')): ?>
                                                Analytics
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                </li>
                            </ol>
                        </nav>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Page Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <!-- Flash Messages -->
            <?php if(session('success')): ?>
                <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                    <button type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                        </svg>
                    </button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                    <button type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                        </svg>
                    </button>
                </div>
            <?php endif; ?>

            <?php if($errors->any()): ?>
                <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <ul class="list-disc list-inside">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                    <button type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3" onclick="this.parentElement.style.display='none'">
                        <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                        </svg>
                    </button>
                </div>
            <?php endif; ?>

            <?php echo $__env->yieldContent('content'); ?>
        </main>
    </div>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/layouts/admin.blade.php ENDPATH**/ ?>