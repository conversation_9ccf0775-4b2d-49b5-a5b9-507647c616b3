<?php $__env->startSection('title', 'Posts Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Page Header -->
    <div class="admin-card p-8 mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                    Posts Management
                </h1>
                <p class="text-gray-600 mt-2 text-lg">Monitor and moderate all posts in the system</p>
                <div class="flex items-center mt-3 text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
                    </svg>
                    Content moderation and engagement analytics
                </div>
            </div>
            <div class="text-right">
                <div class="bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-3 rounded-xl shadow-lg">
                    <p class="text-sm font-medium opacity-90">Total Posts</p>
                    <p class="text-2xl font-bold"><?php echo e(number_format($post_stats['total'])); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Post Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-blue-600"><?php echo e($post_stats['total']); ?></p>
                <p class="text-sm text-gray-600">Total Posts</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600"><?php echo e($post_stats['published']); ?></p>
                <p class="text-sm text-gray-600">Published</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-gray-600"><?php echo e($post_stats['draft']); ?></p>
                <p class="text-sm text-gray-600">Drafts</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-yellow-600"><?php echo e($post_stats['pending_approval']); ?></p>
                <p class="text-sm text-gray-600">Pending</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-green-600"><?php echo e($post_stats['approved']); ?></p>
                <p class="text-sm text-gray-600">Approved</p>
            </div>
        </div>
        <div class="bg-white shadow rounded-lg p-4">
            <div class="text-center">
                <p class="text-2xl font-bold text-red-600"><?php echo e($post_stats['rejected']); ?></p>
                <p class="text-sm text-gray-600">Rejected</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white shadow rounded-lg p-6">
        <form method="GET" action="<?php echo e(route('admin.posts.index')); ?>" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Title or content..." 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                    <select name="status" id="status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Status</option>
                        <option value="published" <?php echo e(request('status') === 'published' ? 'selected' : ''); ?>>Published</option>
                        <option value="draft" <?php echo e(request('status') === 'draft' ? 'selected' : ''); ?>>Draft</option>
                    </select>
                </div>

                <!-- Approval Status Filter -->
                <div>
                    <label for="approval_status" class="block text-sm font-medium text-gray-700">Approval</label>
                    <select name="approval_status" id="approval_status" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Approval</option>
                        <option value="pending" <?php echo e(request('approval_status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="approved" <?php echo e(request('approval_status') === 'approved' ? 'selected' : ''); ?>>Approved</option>
                        <option value="rejected" <?php echo e(request('approval_status') === 'rejected' ? 'selected' : ''); ?>>Rejected</option>
                    </select>
                </div>

                <!-- Type Filter -->
                <div>
                    <label for="type" class="block text-sm font-medium text-gray-700">Type</label>
                    <select name="type" id="type" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="">All Types</option>
                        <option value="text" <?php echo e(request('type') === 'text' ? 'selected' : ''); ?>>Text</option>
                        <option value="image" <?php echo e(request('type') === 'image' ? 'selected' : ''); ?>>Image</option>
                        <option value="video" <?php echo e(request('type') === 'video' ? 'selected' : ''); ?>>Video</option>
                        <option value="link" <?php echo e(request('type') === 'link' ? 'selected' : ''); ?>>Link</option>
                    </select>
                </div>

                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        Filter
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Posts Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Posts (<?php echo e($posts->total()); ?>)</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Post</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Author</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Context</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Engagement</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="max-w-xs">
                                    <div class="text-sm font-medium text-gray-900 truncate">
                                        <?php echo e($post->title ?: 'Untitled Post'); ?>

                                    </div>
                                    <div class="text-sm text-gray-500 truncate">
                                        <?php echo e(Str::limit(strip_tags($post->content), 100)); ?>

                                    </div>
                                    <div class="flex items-center mt-1 space-x-2">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            <?php if($post->type === 'text'): ?> bg-blue-100 text-blue-800
                                            <?php elseif($post->type === 'image'): ?> bg-green-100 text-green-800
                                            <?php elseif($post->type === 'video'): ?> bg-purple-100 text-purple-800
                                            <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                            <?php echo e(ucfirst($post->type)); ?>

                                        </span>
                                        <?php if($post->is_pinned): ?>
                                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Pinned
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8">
                                        <div class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-700"><?php echo e(substr($post->user->name, 0, 1)); ?></span>
                                        </div>
                                    </div>
                                    <div class="ml-3">
                                        <div class="text-sm font-medium text-gray-900"><?php echo e($post->user->name); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo e($post->user->email); ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php if($post->organization): ?>
                                    <div class="text-purple-600"><?php echo e($post->organization->name); ?></div>
                                <?php elseif($post->group): ?>
                                    <div class="text-blue-600"><?php echo e($post->group->name); ?></div>
                                <?php else: ?>
                                    <div class="text-gray-500">Personal</div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="space-y-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php if($post->status === 'published'): ?> bg-green-100 text-green-800
                                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                        <?php echo e(ucfirst($post->status)); ?>

                                    </span>
                                    <br>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        <?php if($post->approval_status === 'approved'): ?> bg-green-100 text-green-800
                                        <?php elseif($post->approval_status === 'pending'): ?> bg-yellow-100 text-yellow-800
                                        <?php elseif($post->approval_status === 'rejected'): ?> bg-red-100 text-red-800
                                        <?php else: ?> bg-gray-100 text-gray-800 <?php endif; ?>">
                                        <?php echo e(ucfirst($post->approval_status ?: 'None')); ?>

                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="space-y-1">
                                    <div><?php echo e($post->reactions_count); ?> reactions</div>
                                    <div><?php echo e($post->comments_count); ?> comments</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo e($post->created_at->format('M d, Y')); ?>

                                <br>
                                <span class="text-xs"><?php echo e($post->created_at->format('H:i')); ?></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex flex-col space-y-1">
                                    <a href="<?php echo e(route('posts.show', $post)); ?>" class="text-indigo-600 hover:text-indigo-900">View</a>
                                    <?php if($post->approval_status === 'pending'): ?>
                                        <button class="text-green-600 hover:text-green-900 text-left">Approve</button>
                                        <button class="text-red-600 hover:text-red-900 text-left">Reject</button>
                                    <?php endif; ?>
                                    <button class="text-yellow-600 hover:text-yellow-900 text-left">Edit</button>
                                    <button class="text-red-600 hover:text-red-900 text-left">Delete</button>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                No posts found matching your criteria.
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($posts->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($posts->appends(request()->query())->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\LARAVEL_PROJECTS\lara_unilink\resources\views/admin/posts/index.blade.php ENDPATH**/ ?>